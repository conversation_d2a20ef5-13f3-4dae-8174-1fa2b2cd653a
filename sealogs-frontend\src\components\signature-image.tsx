'use client'
import React, { useState, useEffect } from 'react'
import AWS from 'aws-sdk'

const ACCOUNT_ID = 'ddde1c1cd1aa25641691808dcbafdeb7'
const ACCESS_KEY_ID = '06c3e13a539f24e6fdf7075bf381bf5e'
const SECRET_ACCESS_KEY =
    '0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8'

const s3Client = new AWS.S3({
    endpoint: `https://${ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: ACCESS_KEY_ID,
    secretAccessKey: SECRET_ACCESS_KEY,
    signatureVersion: 'v4',
    region: 'auto',
})

export default function SignatureImage(props: any) {
    const [imageUrl, setImageUrl] = useState<string | null>(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const fetchSignature = async () => {
            if (!props.id) {
                setLoading(false)
                return
            }

            const fileName = 'signature/' + props.id + '.png'

            try {
                const data = await s3Client
                    .getObject({
                        Bucket: 'signature',
                        Key: fileName,
                    })
                    .promise()

                if (data.Body) {
                    const base64Data = Buffer.from(
                        data.Body as Uint8Array,
                    ).toString('base64')
                    const dataUrl = `data:image/png;base64,${base64Data}`
                    setImageUrl(dataUrl)
                }
            } catch (err) {
                console.error(err)
            } finally {
                setLoading(false)
            }
        }

        fetchSignature()
    }, [props.id])

    if (loading) {
        return <div>Loading...</div>
    }

    if (!imageUrl) {
        return <></>
    }

    return (
        <img
            src={imageUrl}
            alt={props?.alt}
            width={props?.width}
            height={props?.height}
            className={props?.className}
        />
    )
}
